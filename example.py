import langextract as lx
import textwrap

# 1. Define the prompt and extraction rules
prompt = textwrap.dedent("""\
    Extract characters, emotions, and relationships in order of appearance.
    Use exact text for extractions. Do not paraphrase or overlap entities.
    Provide meaningful attributes for each entity to add context.""")

# 2. Provide a high-quality example to guide the model
examples = [
    lx.data.ExampleData(
        text="ROMEO. But soft! What light through yonder window breaks? It is the east, and Juliet is the sun.",
        extractions=[
            lx.data.Extraction(
                extraction_class="character",
                extraction_text="ROMEO",
                attributes={"emotional_state": "wonder"}
            ),
            lx.data.Extraction(
                extraction_class="emotion",
                extraction_text="But soft!",
                attributes={"feeling": "gentle awe"}
            ),
            lx.data.Extraction(
                extraction_class="relationship",
                extraction_text="Juliet is the sun",
                attributes={"type": "metaphor"}
            ),
        ]
    )
]

# The input text to be processed
input_text = "Lady Juliet gazed longingly at the stars, her heart aching for <PERSON>"

# Run the extraction
result = lx.extract(
    text_or_documents=input_text,
    prompt_description=prompt,
    examples=examples,
    language_model_type=lx.inference.GeminiLanguageModel,
    # language_model_type=lx.inference.OpenAILanguageModel,
    # language_model_type=lx.inference.OllamaLanguageModel,
    # model_url="https://ai.kevin2li.top/v1",
    # api_key="sk-YDiVjFpKPyRCHOLrixDhsD9I7OCxN87H0GnjCkaAMEGE4oi4",
    api_key="AIzaSyADgpHuRvQ9MrrnSc65VUYpDtrfKevApSk",
    model_id="gemini-2.5-flash",
)


# Process Romeo & Juliet directly from Project Gutenberg
# result = lx.extract(
#     text_or_documents="https://www.gutenberg.org/files/1513/1513-0.txt",
#     prompt_description=prompt,
#     examples=examples,
#     model_url="",
#     api_key="",
#     model_id="gemini-2.5-flash",
#     extraction_passes=3,    # Improves recall through multiple passes
#     max_workers=20,         # Parallel processing for speed
#     max_char_buffer=1000    # Smaller contexts for better accuracy
# )

print(result)