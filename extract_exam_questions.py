import langextract as lx
import textwrap
import re
import json
from typing import List, Dict, Any

def extract_exam_questions(file_path: str, api_key: str = None) -> List[Dict[str, Any]]:
    """
    Extract structured information from exam questions using langextract.
    
    Args:
        file_path: Path to the text file containing exam questions
        api_key: API key for the language model (optional, uses default from example.py)
    
    Returns:
        List of dictionaries containing extracted question information
    """
    
    # Read the input file
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            text_content = f.read()
    except FileNotFoundError:
        print(f"Error: File {file_path} not found.")
        return []
    except Exception as e:
        print(f"Error reading file: {e}")
        return []
    
    # Define the extraction prompt
    prompt = textwrap.dedent("""\
        Extract exam question components in order of appearance.
        For each question, identify:
        1. Question stem (题干) - the main question text including question number
        2. Answer options (选项) - multiple choice options (A, B, C, D, etc.)
        3. Correct answer (答案) - the correct answer choice indicated in parentheses
        4. Explanation (解析) - detailed explanations if present
        
        Use exact text for extractions. Do not paraphrase or overlap entities.
        Provide meaningful attributes for each entity to add context.""")
    
    # Provide examples to guide the model
    examples = [
        lx.data.ExampleData(
            text="1、Internet中发送邮件协议是（B ）。\nA、FTP B、SMTP　\nC、HTTP　 D、POP\n解析：这道题考察了xx知识点。",
            extractions=[
                lx.data.Extraction(
                    extraction_class="question_stem",
                    extraction_text="1、Internet中发送邮件协议是（B ）。",
                    attributes={"question_number": "1", "correct_answer": "B"}
                ),
                lx.data.Extraction(
                    extraction_class="answer_option",
                    extraction_text="A、FTP",
                    attributes={"option_letter": "A", "option_text": "FTP"}
                ),
                lx.data.Extraction(
                    extraction_class="answer_option",
                    extraction_text="B、SMTP",
                    attributes={"option_letter": "B", "option_text": "SMTP"}
                ),
                lx.data.Extraction(
                    extraction_class="answer_option",
                    extraction_text="C、HTTP",
                    attributes={"option_letter": "C", "option_text": "HTTP"}
                ),
                lx.data.Extraction(
                    extraction_class="answer_option",
                    extraction_text="D、POP",
                    attributes={"option_letter": "D", "option_text": "POP"}
                ),
                lx.data.Extraction(
                    extraction_class="correct_answer",
                    extraction_text="B",
                    attributes={"answer": "B", "answer_text": "SMTP"}
                ),
                lx.data.Extraction(
                    extraction_class="explanation",
                    extraction_text="解析：这道题考察了xx知识点。",
                    attributes={"type": "explanation"}
                ),
            ]
        )
    ]
    
    # Use the API key from example.py if not provided
    if api_key is None:
        api_key = "AIzaSyADgpHuRvQ9MrrnSc65VUYpDtrfKevApSk"
    
    try:
        # Run the extraction
        result = lx.extract(
            text_or_documents=text_content,
            prompt_description=prompt,
            examples=examples,
            language_model_type=lx.inference.GeminiLanguageModel,
            api_key=api_key,
            model_id="gemini-2.5-flash",
        )
        
        return result
        
    except Exception as e:
        print(f"Error during extraction: {e}")
        return []

def parse_extraction_results(result) -> List[Dict[str, Any]]:
    """
    Parse the langextract results into structured question data.
    
    Args:
        result: The result from langextract
    
    Returns:
        List of structured question dictionaries
    """
    questions = []
    current_question = {}
    
    if hasattr(result, 'extractions'):
        extractions = result.extractions
    else:
        extractions = result
    
    for extraction in extractions:
        extraction_class = extraction.extraction_class
        extraction_text = extraction.extraction_text
        attributes = extraction.attributes if hasattr(extraction, 'attributes') else {}
        
        if extraction_class == "question_stem":
            # Start a new question
            if current_question:
                questions.append(current_question)
            
            current_question = {
                "question_number": attributes.get("question_number", ""),
                "question_stem": extraction_text,
                "correct_answer": attributes.get("correct_answer", ""),
                "answer_options": [],
                "explanation": ""
            }
        
        elif extraction_class == "answer_option":
            if current_question:
                current_question["answer_options"].append({
                    "option_letter": attributes.get("option_letter", ""),
                    "option_text": attributes.get("option_text", ""),
                    "full_text": extraction_text
                })
        
        elif extraction_class == "explanation":
            if current_question:
                current_question["explanation"] = extraction_text
    
    # Add the last question
    if current_question:
        questions.append(current_question)
    
    return questions

def format_output(questions: List[Dict[str, Any]]) -> str:
    """
    Format the extracted questions into a readable output.
    
    Args:
        questions: List of structured question dictionaries
    
    Returns:
        Formatted string output
    """
    output = []
    output.append("=" * 60)
    output.append("EXTRACTED EXAM QUESTIONS")
    output.append("=" * 60)
    
    for i, question in enumerate(questions, 1):
        output.append(f"\n问题 {i}:")
        output.append("-" * 40)
        output.append(f"题干: {question.get('question_stem', 'N/A')}")
        output.append(f"正确答案: {question.get('correct_answer', 'N/A')}")
        
        output.append("\n选项:")
        for option in question.get('answer_options', []):
            output.append(f"  {option.get('full_text', 'N/A')}")
        
        if question.get('explanation'):
            output.append(f"\n解析: {question.get('explanation')}")
        
        output.append("")
    
    return "\n".join(output)

def main():
    """Main function to run the extraction."""
    file_path = "test.txt"
    
    print("Starting exam question extraction...")
    print(f"Processing file: {file_path}")
    
    # Extract using langextract
    result = extract_exam_questions(file_path)
    
    if not result:
        print("No extraction results obtained.")
        return
    
    # Parse the results
    questions = parse_extraction_results(result)
    
    # Format and display output
    formatted_output = format_output(questions)
    print(formatted_output)
    
    # Save to JSON file for further processing
    try:
        with open("extracted_questions.json", "w", encoding="utf-8") as f:
            json.dump(questions, f, ensure_ascii=False, indent=2)
        print(f"\nExtracted data saved to: extracted_questions.json")
    except Exception as e:
        print(f"Error saving JSON file: {e}")
    
    print(f"\nTotal questions extracted: {len(questions)}")

if __name__ == "__main__":
    main()
