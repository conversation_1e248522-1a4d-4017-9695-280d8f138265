# Exam Question Extractor

This project provides Python scripts to extract structured information from exam questions or quiz content in Chinese. The scripts can parse and extract the following components:

1. **Question stems** (题干) - the main question text
2. **Answer options** (选项) - multiple choice options (A, B, C, D, etc.)
3. **Correct answers** (答案) - the correct answer choices
4. **Explanations** (解析) - detailed explanations or reasoning for the answers

## Files

- `extract_exam_questions.py` - Main extraction script using langextract (AI-powered)
- `extract_exam_questions_regex.py` - Alternative extraction script using regular expressions
- `test.txt` - Sample exam questions file
- `example.py` - Reference implementation showing langextract usage patterns

## Requirements

### For AI-powered extraction (recommended):
- Python 3.6+
- langextract package
- API key for Gemini language model

### For regex-based extraction:
- Python 3.6+ (no additional packages required)

## Usage

### Method 1: AI-powered extraction (Recommended)

```bash
python3 extract_exam_questions.py
```

This method uses the langextract library with Google's Gemini model to intelligently parse the exam questions. It provides:
- High accuracy in parsing complex question formats
- Better handling of irregular formatting
- Contextual understanding of question components

### Method 2: Regex-based extraction

```bash
python3 extract_exam_questions_regex.py
```

This method uses regular expressions for parsing. It's faster but less accurate for complex formats.

## Input Format

The scripts expect exam questions in the following format:

```
1、Internet中发送邮件协议是（B ）。
A、FTP B、SMTP　
C、HTTP　 D、POP
解析：这道题考察了xx知识点。

2、在 OSI模型中，第 N层和其上的 N＋ l层的关系是 (A)
A、N层为N十1层提供服务
B、N十1层将从N层接收的信息增加了一个头
C、N层利用N十1层提供的服务
D、N层对N＋1层没有任何作用
```

## Output

Both scripts generate:

1. **Console output**: Formatted display of extracted questions
2. **JSON file**: Structured data saved as `extracted_questions.json` or `extracted_questions_regex.json`

### JSON Structure

```json
[
  {
    "question_number": "1",
    "question_stem": "1、Internet中发送邮件协议是（B ）。",
    "correct_answer": "B",
    "answer_options": [
      {
        "option_letter": "A",
        "option_text": "FTP",
        "full_text": "A、FTP"
      },
      {
        "option_letter": "B",
        "option_text": "SMTP",
        "full_text": "B、SMTP"
      }
    ],
    "explanation": "解析：这道题考察了xx知识点。"
  }
]
```

## Configuration

### API Key Setup

The AI-powered extraction requires a Gemini API key. You can:

1. Use the default key in the script (for testing)
2. Pass your own API key to the `extract_exam_questions()` function
3. Modify the script to read from environment variables

### Customization

You can customize the extraction by modifying:

- **Prompt description**: Change the extraction instructions
- **Examples**: Provide different example patterns
- **Language model**: Switch between different AI models
- **Output format**: Modify the formatting functions

## Performance Comparison

| Method | Accuracy | Speed | Dependencies | Best for |
|--------|----------|-------|--------------|----------|
| AI-powered | High | Slower | langextract, API key | Complex formats, high accuracy needs |
| Regex-based | Medium | Fast | None | Simple formats, quick processing |

## Example Results

From the test.txt file, the AI-powered extraction successfully identified:
- 6 questions with complete information
- All answer options correctly parsed
- Correct answers properly extracted
- Explanations captured where present

## Error Handling

Both scripts include error handling for:
- File not found errors
- Encoding issues
- API failures (AI method)
- Malformed input data

## Future Improvements

- Support for different question numbering formats
- Multi-language support
- Batch processing of multiple files
- Integration with exam management systems
- Export to different formats (CSV, Excel, etc.)
