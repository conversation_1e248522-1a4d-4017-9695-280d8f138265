import re
import json
from typing import List, Dict, Any

def extract_exam_questions_regex(file_path: str) -> List[Dict[str, Any]]:
    """
    Extract structured information from exam questions using regular expressions.
    This is a fallback method that doesn't require langextract.
    
    Args:
        file_path: Path to the text file containing exam questions
    
    Returns:
        List of dictionaries containing extracted question information
    """
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print(f"Error: File {file_path} not found.")
        return []
    except Exception as e:
        print(f"Error reading file: {e}")
        return []
    
    questions = []
    
    # Split content into lines and process
    lines = content.strip().split('\n')
    current_question = None
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
        
        # Match question stem (starts with number and contains answer in parentheses)
        question_match = re.match(r'^(\d+)[、．](.+?)（([A-Z])\s*）(.*)$', line)
        if question_match:
            # Save previous question if exists
            if current_question:
                questions.append(current_question)
            
            question_num = question_match.group(1)
            question_text = question_match.group(2).strip()
            correct_answer = question_match.group(3)
            remaining_text = question_match.group(4).strip()
            
            current_question = {
                "question_number": question_num,
                "question_stem": line,
                "correct_answer": correct_answer,
                "answer_options": [],
                "explanation": ""
            }
            continue
        
        # Match answer options (A、B、C、D followed by content)
        option_match = re.match(r'^([A-Z])[、．](.+)$', line)
        if option_match and current_question:
            option_letter = option_match.group(1)
            option_text = option_match.group(2).strip()
            
            current_question["answer_options"].append({
                "option_letter": option_letter,
                "option_text": option_text,
                "full_text": line
            })
            continue
        
        # Match explanations (starts with 解析：)
        if line.startswith('解析：') and current_question:
            current_question["explanation"] = line
            continue
    
    # Add the last question
    if current_question:
        questions.append(current_question)
    
    return questions

def format_output_regex(questions: List[Dict[str, Any]]) -> str:
    """
    Format the extracted questions into a readable output.
    
    Args:
        questions: List of structured question dictionaries
    
    Returns:
        Formatted string output
    """
    output = []
    output.append("=" * 60)
    output.append("EXTRACTED EXAM QUESTIONS (REGEX METHOD)")
    output.append("=" * 60)
    
    for i, question in enumerate(questions, 1):
        output.append(f"\n问题 {i}:")
        output.append("-" * 40)
        output.append(f"题干: {question.get('question_stem', 'N/A')}")
        output.append(f"正确答案: {question.get('correct_answer', 'N/A')}")
        
        output.append("\n选项:")
        for option in question.get('answer_options', []):
            output.append(f"  {option.get('full_text', 'N/A')}")
        
        if question.get('explanation'):
            output.append(f"\n解析: {question.get('explanation')}")
        
        output.append("")
    
    return "\n".join(output)

def main():
    """Main function to run the regex-based extraction."""
    file_path = "test.txt"
    
    print("Starting exam question extraction (regex method)...")
    print(f"Processing file: {file_path}")
    
    # Extract using regex
    questions = extract_exam_questions_regex(file_path)
    
    if not questions:
        print("No questions extracted.")
        return
    
    # Format and display output
    formatted_output = format_output_regex(questions)
    print(formatted_output)
    
    # Save to JSON file for further processing
    try:
        with open("extracted_questions_regex.json", "w", encoding="utf-8") as f:
            json.dump(questions, f, ensure_ascii=False, indent=2)
        print(f"\nExtracted data saved to: extracted_questions_regex.json")
    except Exception as e:
        print(f"Error saving JSON file: {e}")
    
    print(f"\nTotal questions extracted: {len(questions)}")

if __name__ == "__main__":
    main()
